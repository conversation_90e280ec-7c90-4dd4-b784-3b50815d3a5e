import { Form } from "react-bootstrap";

const ToggleTools = () => {
  return (
    <Form.Group
      className={`m-0 p-0 file-input-label voice position-absolute`}
    >
      <Form.Check
        type="switch"
        // className={`${reportForm.enable_privacy ? "expanded" : ""} p-0`}
        // checked={reportForm.enable_privacy}
        // onChange={(evt) =>
        //   setReportForm({
        //     ...reportForm,
        //     enable_privacy: evt.target.checked,
        //   })
        // }
      />
    </Form.Group>
  );
};

export default ToggleTools;
